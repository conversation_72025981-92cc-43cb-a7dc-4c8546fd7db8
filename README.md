# LibreNMS Device List Extractor

This collection of scripts helps you extract device information from your LibreNMS database, specifically:
- **Name** (Device display name)
- **IP Address** 
- **Model** (Hardware model)
- **Make** (Manufacturer)
- **Serial Number**

## Files Included

1. **`device_list_query.sql`** - Direct SQL query file
2. **`get_device_list.py`** - Python script with advanced features
3. **`db_config.py`** - Database configuration file for Python script
4. **`run_device_query.bat`** - Windows batch script
5. **`Get-DeviceList.ps1`** - PowerShell script
6. **`README.md`** - This instruction file

## Quick Start

### Option 1: Direct SQL Query (Simplest)

1. Open your MySQL client or phpMyAdmin
2. Connect to your LibreNMS database
3. Run the query from `device_list_query.sql`

### Option 2: PowerShell Script (Recommended for Windows)

1. Edit `Get-DeviceList.ps1` and update the database parameters:
   ```powershell
   $Server = "your_database_host"
   $Username = "your_username" 
   $Password = "your_password"
   ```

2. Run the script:
   ```powershell
   .\Get-DeviceList.ps1
   ```

### Option 3: Python Script (Most Features)

1. Install required Python packages:
   ```bash
   pip install mysql-connector-python tabulate
   ```

2. Edit `db_config.py` with your database settings:
   ```python
   DB_CONFIG = {
       'host': 'your_host',
       'database': 'librenms',
       'user': 'your_username',
       'password': 'your_password',
       'port': 3306
   }
   ```

3. Run the script:
   ```bash
   python get_device_list.py
   ```

## Database Connection Settings

You'll need to update the connection settings in whichever method you choose:

- **Host**: Usually `localhost` if running on the same server as LibreNMS
- **Database**: Usually `librenms`
- **Username**: Your LibreNMS database username
- **Password**: Your LibreNMS database password
- **Port**: Usually `3306` for MySQL

## Output

All methods will provide:
- Console/terminal output showing the device list in a formatted table
- CSV file export (`device_list.csv`) for use in Excel or other applications

## Sample Output

```
Device Name          IP Address      Model           Make      Serial Number
CORE-RTR            ************    FGT_200F        Fortinet  FG200FT920909753
CORE-SW01           ************    S2900-24S8C4X   BDCOM     20073022410
ADM-DLS-SW01        ************    CBS350-24P-4G   Cisco     FOC2720YAT0
```

## Troubleshooting

### Common Issues:

1. **Connection refused**: Check if MySQL service is running and accessible
2. **Access denied**: Verify username/password and database permissions
3. **Table doesn't exist**: Ensure you're connecting to the correct LibreNMS database
4. **Empty results**: Check if devices exist and are not ignored in LibreNMS

### Database Permissions Required:

Your database user needs `SELECT` permissions on:
- `devices` table
- `entPhysical` table

### For Python Script:
- Install MySQL connector: `pip install mysql-connector-python`
- Install tabulate for formatting: `pip install tabulate`

### For PowerShell Script:
- Requires MySQL command line tools in system PATH
- Or MySQL .NET Connector for direct database access

## Customization

You can modify the SQL query in any of the files to:
- Include additional fields
- Filter by device type or location
- Change sorting order
- Include inactive devices

## Security Note

Remember to:
- Use strong database passwords
- Limit database user permissions to only what's needed
- Don't commit passwords to version control
- Consider using environment variables for sensitive data
