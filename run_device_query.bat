@echo off
R<PERSON> Batch script to run device list query against LibreNMS database
REM Update the connection parameters below to match your setup

echo LibreNMS Device List Extractor
echo ================================

REM Database connection parameters - UPDATE THESE!
set DB_HOST=localhost
set DB_USER=librenms
set DB_PASSWORD=your_password
set DB_NAME=librenms

echo Connecting to database %DB_HOST%...
echo.

REM Run the SQL query and output to both console and CSV file
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < device_list_query.sql

REM Also create CSV output
echo Creating CSV export...
mysql -h %DB_HOST% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% -B -e "SELECT DISTINCT COALESCE(d.display, d.sysName, d.hostname) as 'Device_Name', COALESCE(d.overwrite_ip, d.hostname) as 'IP_Address', d.hardware as 'Model', ep.entPhysicalMfgName as 'Make', COALESCE(d.serial, ep.entPhysicalSerialNum) as 'Serial_Number' FROM devices d LEFT JOIN entPhysical ep ON d.device_id = ep.device_id AND ep.entPhysicalClass = 'chassis' WHERE d.status = 1 AND d.ignore = 0 ORDER BY 'Device_Name';" | sed 's/\t/,/g' > device_list.csv

echo.
echo Results saved to device_list.csv
echo.
pause
