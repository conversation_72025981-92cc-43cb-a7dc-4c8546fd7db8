# PowerShell script to extract device list from LibreNMS database
# Requires MySQL .NET Connector or direct MySQL command line tools

param(
    [string]$Server = "localhost",
    [string]$Database = "librenms", 
    [string]$Username = "librenms",
    [string]$Password = "your_password",
    [string]$OutputFile = "device_list.csv"
)

Write-Host "LibreNMS Device List Extractor" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# SQL Query
$query = @"
SELECT DISTINCT
    COALESCE(d.display, d.sysName, d.hostname) as 'Device Name',
    COALESCE(d.overwrite_ip, d.hostname) as 'IP Address',
    d.hardware as 'Model',
    ep.entPhysicalMfgName as 'Make',
    COALESCE(d.serial, ep.entPhysicalSerialNum) as 'Serial Number'
FROM devices d
LEFT JOIN entPhysical ep ON d.device_id = ep.device_id 
    AND ep.entPhysicalClass = 'chassis'
WHERE d.status = 1
    AND d.ignore = 0
ORDER BY 'Device Name';
"@

try {
    # Method 1: Using MySQL command line (requires mysql.exe in PATH)
    Write-Host "Attempting to connect to database..." -ForegroundColor Yellow
    
    # Create temporary SQL file
    $tempSqlFile = [System.IO.Path]::GetTempFileName() + ".sql"
    $query | Out-File -FilePath $tempSqlFile -Encoding UTF8
    
    # Execute MySQL command
    $mysqlCmd = "mysql -h $Server -u $Username -p$Password $Database"
    $result = & cmd /c "$mysqlCmd < `"$tempSqlFile`""
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Query executed successfully!" -ForegroundColor Green
        Write-Host $result
        
        # Export to CSV
        $csvQuery = $query -replace "'", '"'
        $csvResult = & cmd /c "$mysqlCmd -B -e `"$csvQuery`"" | ConvertFrom-Csv -Delimiter "`t"
        $csvResult | Export-Csv -Path $OutputFile -NoTypeInformation
        
        Write-Host ""
        Write-Host "Results exported to: $OutputFile" -ForegroundColor Green
    } else {
        Write-Host "Error executing MySQL command. Please check your connection parameters." -ForegroundColor Red
    }
    
    # Clean up temp file
    Remove-Item $tempSqlFile -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Make sure you have:" -ForegroundColor Yellow
    Write-Host "1. MySQL command line tools installed and in PATH" -ForegroundColor Yellow
    Write-Host "2. Correct database connection parameters" -ForegroundColor Yellow
    Write-Host "3. Network access to the database server" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Script completed." -ForegroundColor Green
