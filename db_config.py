# Database Configuration for LibreNMS
# Update these settings to match your database configuration

DB_CONFIG = {
    'host': 'localhost',        # Database host (usually localhost)
    'database': 'librenms',     # Database name
    'user': 'librenms',         # Database username
    'password': 'your_password', # Database password - CHANGE THIS!
    'port': 3306,               # Database port (default MySQL port)
    'charset': 'utf8mb4'
}

# Alternative configuration for different environments
CONFIGS = {
    'local': {
        'host': 'localhost',
        'database': 'librenms',
        'user': 'librenms',
        'password': 'your_password',
        'port': 3306
    },
    'remote': {
        'host': 'your_remote_host',
        'database': 'librenms', 
        'user': 'librenms',
        'password': 'your_password',
        'port': 3306
    }
}
