#!/usr/bin/env python3
"""
Script to retrieve device information from LibreNMS database
Extracts: Name, IP, Model, Make, Serial Number
"""

import mysql.connector
from mysql.connector import Error
import socket
import struct
from tabulate import tabulate
import sys
import os
from db_config import DB_CONFIG

def ip_from_binary(binary_ip):
    """Convert binary IP address to string format"""
    if not binary_ip:
        return ""
    
    try:
        if len(binary_ip) == 4:  # IPv4
            return socket.inet_ntoa(binary_ip)
        elif len(binary_ip) == 16:  # IPv6
            return socket.inet_ntop(socket.AF_INET6, binary_ip)
        else:
            return ""
    except:
        return ""

def connect_to_database():
    """Connect to the LibreNMS database"""
    try:
        # Use configuration from db_config.py
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            print("Successfully connected to LibreNMS database")
            return connection
            
    except Error as e:
        print(f"Error connecting to database: {e}")
        return None

def get_device_list(connection):
    """Retrieve device list with Name, IP, Model, Make, Serial Number"""
    
    query = """
    SELECT DISTINCT
        COALESCE(d.display, d.sysName, d.hostname) as Name,
        COALESCE(d.overwrite_ip, d.hostname) as IP,
        d.hardware as Model,
        ep.entPhysicalMfgName as Make,
        COALESCE(d.serial, ep.entPhysicalSerialNum) as Serial_Number,
        d.ip as binary_ip
    FROM devices d
    LEFT JOIN entPhysical ep ON d.device_id = ep.device_id 
        AND ep.entPhysicalClass = 'chassis'
    WHERE d.status = 1  -- Only active devices
        AND d.ignore = 0  -- Not ignored devices
    ORDER BY Name
    """
    
    try:
        cursor = connection.cursor()
        cursor.execute(query)
        results = cursor.fetchall()
        
        # Process results to handle binary IP addresses
        processed_results = []
        for row in results:
            name, ip, model, make, serial, binary_ip = row
            
            # If IP is empty or looks like binary, try to convert binary IP
            if not ip or ip.startswith('192.168') == False:
                converted_ip = ip_from_binary(binary_ip) if binary_ip else ip
                if converted_ip:
                    ip = converted_ip
            
            processed_results.append([
                name or "N/A",
                ip or "N/A", 
                model or "N/A",
                make or "N/A",
                serial or "N/A"
            ])
        
        return processed_results
        
    except Error as e:
        print(f"Error executing query: {e}")
        return []
    finally:
        if cursor:
            cursor.close()

def main():
    """Main function"""
    print("LibreNMS Device List Extractor")
    print("=" * 50)
    
    # Connect to database
    connection = connect_to_database()
    if not connection:
        print("Failed to connect to database. Please check your connection settings.")
        sys.exit(1)
    
    try:
        # Get device list
        devices = get_device_list(connection)
        
        if not devices:
            print("No devices found or error retrieving data.")
            return
        
        # Display results in table format
        headers = ["Name", "IP Address", "Model", "Make", "Serial Number"]
        print(f"\nFound {len(devices)} devices:")
        print(tabulate(devices, headers=headers, tablefmt="grid"))
        
        # Also save to CSV file
        import csv
        with open('device_list.csv', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            writer.writerows(devices)
        
        print(f"\nData also saved to 'device_list.csv'")
        
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("Database connection closed.")

if __name__ == "__main__":
    main()
