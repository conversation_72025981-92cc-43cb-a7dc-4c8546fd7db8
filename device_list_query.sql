-- LibreNMS Device List Query
-- Extracts: Name, IP, Model, Make, Serial Number

SELECT DISTINCT
    COALESCE(d.display, d.sysName, d.hostname) as 'Device Name',
    COALESCE(d.overwrite_ip, d.hostname) as 'IP Address',
    d.hardware as 'Model',
    ep.entPhysicalMfgName as 'Make',
    COALESCE(d.serial, ep.entPhysicalSerialNum) as 'Serial Number'
FROM devices d
LEFT JOIN entPhysical ep ON d.device_id = ep.device_id 
    AND ep.entPhysicalClass = 'chassis'
WHERE d.status = 1  -- Only active devices
    AND d.ignore = 0  -- Not ignored devices
ORDER BY 'Device Name';

-- Alternative query if you want to see all devices (including inactive)
-- Just remove the WHERE clause conditions:

/*
SELECT DISTINCT
    COALESCE(d.display, d.sysName, d.hostname) as 'Device Name',
    COALESCE(d.overwrite_ip, d.hostname) as 'IP Address',
    d.hardware as 'Model',
    ep.entPhysicalMfgName as 'Make',
    COALESCE(d.serial, ep.entPhysicalSerialNum) as 'Serial Number',
    CASE 
        WHEN d.status = 1 THEN 'Active'
        ELSE 'Inactive'
    END as 'Status'
FROM devices d
LEFT JOIN entPhysical ep ON d.device_id = ep.device_id 
    AND ep.entPhysicalClass = 'chassis'
ORDER BY 'Device Name';
*/
